import { useEffect, useState } from 'react'
import { ReadAppNotificationRecipients } from './queries'
import { useLazyQuery } from '@apollo/client'
import {
    createColumns,
    DataTable,
    ExtendedColumnDef,
} from '@/components/filteredTable'
import Link from 'next/link'
import { P } from '@/components/ui'

const AppNotification = () => {
    const [notifications, setNotifications] = useState([])
    const [userID, setUserID] = useState(0)
    const [readAppNotificationRecipients] = useLazyQuery(
        ReadAppNotificationRecipients,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readAppNotificationRecipients
                if (data) {
                    setNotifications(data.nodes)
                }
            },
            onError: (error: any) => {
                console.error('readAppNotificationRecipients error', error)
            },
        },
    )
    const loadNotifications = async () => {
        await readAppNotificationRecipients({
            variables: {
                filter: { userID: { eq: userID } },
            },
        })
    }

    useEffect(() => {
        setUserID(+(localStorage.getItem('userId') ?? 0))
    }, [])
    useEffect(() => {
        if (userID > 0) {
            loadNotifications()
        }
    }, [userID])

    // Define columns for the notifications table
    const columns: ExtendedColumnDef<any, any>[] = createColumns([
        {
            accessorKey: 'title',
            header: 'Notification',
            cellAlignment: 'left',
            cell: ({ row }: { row: any }) => {
                const item = row.original
                return (
                    <div className="space-y-1 py-2.5">
                        <div className="flex items-start justify-between">
                            <span className="flex flex-col items-center sm:flex-row gap-x-2.5">
                                {item.notification.targetLink ? (
                                    <Link href={item.notification.targetLink}>
                                        <div className="hover:underline">
                                            {item.notification.message}
                                        </div>
                                    </Link>
                                ) : (
                                    <div>{item.notification.message}</div>
                                )}
                            </span>
                        </div>
                        <P>{item.notification.title}</P>
                    </div>
                )
            },
        },
    ])
    return (
        <div>
            {notifications.length > 0 && (
                <DataTable
                    columns={columns}
                    data={notifications}
                    showToolbar={false}
                    pageSize={20}
                />
            )}
        </div>
    )
}

export default AppNotification
